'use client';

import { Plus, Trash2, MessageSquare, X } from 'lucide-react';

interface Session {
  name: string;
  createTime: string;
  updateTime: string;
}

interface SessionManagerProps {
  sessions: Session[];
  currentSessionId: string;
  onCreateSession: () => void;
  onSwitchSession: (sessionId: string) => void;
  onDeleteSession: (sessionId: string) => void;
  onClose: () => void;
}

interface SessionOutput {
  output: {
    sessions: Session[];
  }
}

export default function SessionManager({
  sessions,
  currentSessionId,
  onCreateSession,
  onSwitchSession,
  onDeleteSession,
  onClose,
}: SessionManagerProps) {
  const getSessionId = (sessionName: string | undefined | null) => {
    if (!sessionName || typeof sessionName !== 'string') {
      return '';
    }
    return sessionName.split('/').pop() || '';
  };

  const formatDate = (dateString: string | undefined | null) => {
    if (!dateString) {
      return 'Unknown';
    }
    const date = new Date(dateString);
    if (isNaN(date.getTime())) {
      return 'Invalid Date';
    }
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  return (
    <div className="w-80 bg-white border-r border-gray-200 flex flex-col h-full">
      {/* Header */}
      <div className="p-4 border-b border-gray-200 flex items-center justify-between">
        <h2 className="text-lg font-semibold text-gray-900">Chat Sessions</h2>
        <button
          onClick={onClose}
          className="p-1 hover:bg-gray-100 rounded-lg transition-colors lg:hidden"
        >
          <X className="w-4 h-4 text-gray-600" />
        </button>
      </div>

      {/* New Session Button */}
      <div className="p-4 border-b border-gray-200">
        <button
          onClick={onCreateSession}
          className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg font-medium hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all duration-200 flex items-center justify-center gap-2"
        >
          <Plus className="w-4 h-4" />
          New Chat
        </button>
      </div>

      {/* Sessions List */}
      <div className="flex-1 overflow-y-auto">
        {sessions.length === 0 ? (
          <div className="p-4 text-center">
            <MessageSquare className="w-8 h-8 text-gray-400 mx-auto mb-2" />
            <p className="text-sm text-gray-600">No chat sessions yet</p>
            <p className="text-xs text-gray-500 mt-1">Create a new chat to get started</p>
          </div>
        ) : (
          <div className="p-2">
            {(sessions || []).filter(session => session && session.name).map((session) => {
              const sessionId = getSessionId(session.name);
              const isActive = sessionId === currentSessionId;
              
              return (
                <div
                  key={session.name}
                  className={`group relative p-3 rounded-lg mb-2 cursor-pointer transition-all duration-200 ${
                    isActive
                      ? 'bg-blue-50 border border-blue-200'
                      : 'hover:bg-gray-50 border border-transparent'
                  }`}
                  onClick={() => onSwitchSession(sessionId)}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        <MessageSquare className={`w-4 h-4 ${isActive ? 'text-blue-600' : 'text-gray-400'}`} />
                        <span className={`text-sm font-medium truncate ${
                          isActive ? 'text-blue-900' : 'text-gray-900'
                        }`}>
                          Chat Session
                        </span>
                      </div>
                      <p className={`text-xs truncate ${
                        isActive ? 'text-blue-700' : 'text-gray-500'
                      }`}>
                        Created: {formatDate(session.createTime)}
                      </p>
                      <p className={`text-xs truncate ${
                        isActive ? 'text-blue-700' : 'text-gray-500'
                      }`}>
                        Updated: {formatDate(session.updateTime)}
                      </p>
                    </div>
                    
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        onDeleteSession(sessionId);
                      }}
                      className="opacity-0 group-hover:opacity-100 p-1 hover:bg-red-100 rounded transition-all duration-200 text-red-600"
                    >
                      <Trash2 className="w-3 h-3" />
                    </button>
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>

      {/* Footer */}
      <div className="p-4 border-t border-gray-200">
        <p className="text-xs text-gray-500 text-center">
          GPA Chat Sessions - Book a support meeting `here`
        </p>
      </div>
    </div>
  );
}

