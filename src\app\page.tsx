'use client';

import { useState, useEffect } from 'react';
import EmailAuth from '@/components/EmailAuth';
import ChatInterface from '@/components/ChatInterface';

export default function Home() {
  const [userEmail, setUserEmail] = useState<string>('');
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);

  useEffect(() => {
    // Check if user is already authenticated (stored in localStorage)
    const storedEmail = localStorage.getItem('userEmail');
    if (storedEmail) {
      setUserEmail(storedEmail);
      setIsAuthenticated(true);
    }
  }, []);

  const handleAuthentication = (email: string) => {
    setUserEmail(email);
    setIsAuthenticated(true);
    localStorage.setItem('userEmail', email);
  };

  const handleLogout = () => {
    setUserEmail('');
    setIsAuthenticated(false);
    localStorage.removeItem('userEmail');
    localStorage.removeItem('currentSessionId');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {!isAuthenticated ? (
        <EmailAuth onAuthenticate={handleAuthentication} />
      ) : (
        <ChatInterface userEmail={userEmail} onLogout={handleLogout} />
      )}
    </div>
  );
}

