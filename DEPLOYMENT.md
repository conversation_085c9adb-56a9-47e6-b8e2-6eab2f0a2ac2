# ADK Chat - Deployment Guide

This guide provides instructions for deploying the ADK Chat application to various platforms.

## Build Status

✅ **Production Build Successful**
- Build completed successfully with only minor warnings
- Application is ready for deployment
- All TypeScript errors resolved
- ESLint warnings are non-critical

## Build Output Summary

```
Route (app)                                 Size  First Load JS    
┌ ○ /                                     4.7 kB         106 kB
├ ○ /_not-found                            977 B         102 kB
├ ƒ /api/chat                              139 B         101 kB
└ ƒ /api/sessions                          139 B         101 kB
+ First Load JS shared by all             101 kB
```

## Deployment Options

### 1. Vercel (Recommended)

Vercel is the easiest platform for deploying Next.js applications:

```bash
# Install Vercel CLI
npm i -g vercel

# Deploy
vercel

# Follow the prompts to configure your deployment
```

**Environment Setup:**
- Upload your `account.json` file as an environment variable
- Configure project settings in Vercel dashboard

### 2. Google Cloud Run

Deploy as a containerized application:

```dockerfile
# Dockerfile
FROM node:18-alpine

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

EXPOSE 3000
CMD ["npm", "start"]
```

```bash
# Build and deploy
gcloud builds submit --tag gcr.io/[PROJECT-ID]/adk-chat
gcloud run deploy --image gcr.io/[PROJECT-ID]/adk-chat --platform managed
```

### 3. Traditional VPS/Server

For deployment on a traditional server:

```bash
# On your server
git clone [your-repo]
cd adk-chat-app
npm install
npm run build

# Use PM2 for process management
npm install -g pm2
pm2 start npm --name "adk-chat" -- start
pm2 startup
pm2 save
```

### 4. Docker Deployment

```bash
# Build Docker image
docker build -t adk-chat .

# Run container
docker run -p 3000:3000 -v /path/to/account.json:/app/account.json adk-chat
```

## Environment Configuration

### Required Files

1. **Service Account JSON** (`account.json`)
   - Place in root directory or configure path in environment
   - Ensure proper permissions for Vertex AI Agent Engine

2. **Environment Variables** (Optional)
   ```bash
   GOOGLE_APPLICATION_CREDENTIALS=/path/to/account.json
   NODE_ENV=production
   PORT=3000
   ```

### Configuration Updates

Before deployment, update these values in `src/lib/vertexai.ts`:

```typescript
private projectId: string = 'your-actual-project-id';
private location: string = 'your-actual-location';
private reasoningEngineId: string = 'your-actual-reasoning-engine-id';
```

## Security Checklist

- [ ] Service account has minimal required permissions
- [ ] Account.json file is not committed to version control
- [ ] CORS is properly configured for your domain
- [ ] Environment variables are properly secured
- [ ] HTTPS is enabled in production

## Performance Optimization

The application is already optimized with:
- Static page generation where possible
- Code splitting and lazy loading
- Optimized bundle sizes
- Efficient API routes

## Monitoring and Logging

Consider implementing:
- Application monitoring (e.g., Sentry)
- Performance monitoring
- API usage tracking
- Error logging and alerting

## Troubleshooting Deployment

### Common Issues

1. **Build Warnings**
   - Viewport metadata warnings are non-critical
   - useEffect dependency warning is acceptable for this use case

2. **Authentication Issues**
   - Verify service account file is accessible
   - Check file permissions and path
   - Ensure service account has correct roles

3. **API Connection Issues**
   - Verify reasoning engine ID is correct
   - Check network connectivity from deployment environment
   - Confirm Vertex AI APIs are enabled

### Health Check Endpoint

The application automatically provides health checks at:
- `/` - Main application
- `/api/sessions` - Session management API
- `/api/chat` - Chat streaming API

## Scaling Considerations

For high-traffic deployments:
- Use multiple instances with load balancing
- Implement connection pooling for API calls
- Consider caching strategies for session data
- Monitor API rate limits and quotas

## Backup and Recovery

- Service account credentials should be backed up securely
- Application configuration should be version controlled
- Consider database backups if extending with persistent storage

---

**Next Steps:**
1. Choose your deployment platform
2. Configure environment variables
3. Update API configuration
4. Deploy and test
5. Monitor and maintain

