import { GoogleAuth } from 'google-auth-library';
import fs from 'fs';
import path from 'path';

export interface ServiceAccountKey {
  type: string;
  project_id: string;
  private_key_id: string;
  private_key: string;
  client_email: string;
  client_id: string;
  auth_uri: string;
  token_uri: string;
  auth_provider_x509_cert_url: string;
  client_x509_cert_url: string;
}

export class GoogleCloudAuth {
  private auth: GoogleAuth;
  private serviceAccountPath: string;

  constructor() {
    // Look for account.json in root directory or env
    this.serviceAccountPath = this.findServiceAccountFile();
    
    this.auth = new GoogleAuth({
      keyFile: this.serviceAccountPath,
      scopes: ['https://www.googleapis.com/auth/cloud-platform'],
    });
  }

  private findServiceAccountFile(): string {
    const possiblePaths = [
      path.join(process.cwd(), 'account.json'),
      path.join(process.cwd(), 'env', 'account.json'),
      path.join(process.cwd(), '.env', 'account.json'),
    ];

    for (const filePath of possiblePaths) {
      if (fs.existsSync(filePath)) {
        return filePath;
      }
    }

    throw new Error('Service account file (account.json) not found. Please place it in the root directory or env folder.');
  }

  async getAccessToken(): Promise<string> {
    try {
      const client = await this.auth.getClient();
      const accessToken = await client.getAccessToken();
      
      if (!accessToken.token) {
        throw new Error('Failed to obtain access token');
      }
      
      return accessToken.token;
    } catch (error) {
      console.error('Error getting access token:', error);
      throw error;
    }
  }

  async getProjectId(): Promise<string> {
    try {
      return await this.auth.getProjectId();
    } catch (error) {
      console.error('Error getting project ID:', error);
      throw error;
    }
  }
}

