# ADK Chat - Vertex AI Agent Engine Interface

A responsive, mobile-friendly Next.js application that interfaces with Google Cloud's Vertex AI Agent Engine using the Agent Development Kit (ADK). This application provides a modern chat interface with session management, email authentication, and real-time streaming responses.

## Features

- **Email Authentication**: Simple email-based user identification
- **Session Management**: Create, list, switch, and delete chat sessions
- **Real-time Streaming**: Server-sent events for streaming AI responses
- **Responsive Design**: Mobile-first design that works on all devices
- **Modern UI**: Clean, professional interface with smooth animations
- **Session Persistence**: Automatic session restoration on page reload

## Prerequisites

Before running this application, you need:

1. **Google Cloud Project** with Vertex AI Agent Engine enabled
2. **Service Account** with appropriate permissions
3. **ADK Agent** deployed to Vertex AI Agent Engine
4. **Node.js** 18+ installed on your system

## Setup Instructions

### 1. Clone and Install Dependencies

```bash
# Navigate to the project directory
cd adk-chat-app

# Install dependencies
npm install
```

### 2. Configure Google Cloud Authentication

Create a service account JSON file and place it in one of these locations:
- `./account.json` (root directory)
- `./env/account.json`
- `./.env/account.json`

**Required Service Account Permissions:**
- Vertex AI User
- Vertex AI Agent Engine User

### 3. Update Configuration

Edit the configuration in `src/lib/vertexai.ts`:

```typescript
private projectId: string = 'your-project-id';
private location: string = 'your-location';
private reasoningEngineId: string = 'your-reasoning-engine-id';
```

Update these values with your actual:
- Google Cloud Project ID
- Location (e.g., 'us-central1')
- Reasoning Engine ID (your deployed ADK agent ID)

### 4. Run the Application

```bash
# Development mode
npm run dev

# Production build
npm run build
npm start
```

The application will be available at `http://localhost:3000`

## API Endpoints

The application provides the following API endpoints:

### Sessions Management (`/api/sessions`)

**POST** - Manage sessions
```json
{
  "action": "create|list|get|delete",
  "user_id": "<EMAIL>",
  "session_id": "session-id-here", // Required for get/delete
  "ttl": "3600s" // Optional for create
}
```

**GET** - List sessions
```
/api/sessions?user_id=<EMAIL>&page_size=10&page_token=token
```

### Chat Streaming (`/api/chat`)

**POST** - Stream chat responses
```json
{
  "user_id": "<EMAIL>",
  "session_id": "session-id-here", // Optional
  "message": "Your message here"
}
```

Returns Server-Sent Events stream with real-time responses.

## Architecture

### Frontend Components

- **EmailAuth**: Handles user email authentication
- **ChatInterface**: Main chat interface with message display
- **SessionManager**: Sidebar for managing chat sessions

### Backend Services

- **GoogleCloudAuth**: Handles service account authentication
- **VertexAIClient**: Interfaces with Vertex AI Agent Engine APIs
- **API Routes**: Next.js API routes for session and chat management

### Key Technologies

- **Next.js 15**: React framework with App Router
- **TypeScript**: Type-safe development
- **Tailwind CSS**: Utility-first CSS framework
- **Lucide Icons**: Modern icon library
- **Google Auth Library**: Google Cloud authentication
- **Axios**: HTTP client for API calls

## Mobile Responsiveness

The application is designed mobile-first with:
- Responsive layout that adapts to screen size
- Touch-friendly interface elements
- Collapsible session manager on mobile
- Optimized typography and spacing
- Smooth animations and transitions

## Error Handling

The application includes comprehensive error handling:
- Authentication failures
- Network connectivity issues
- API rate limiting
- Session management errors
- Streaming connection failures

## Security Considerations

- Service account credentials are server-side only
- User emails are used as identifiers (no sensitive data stored)
- CORS properly configured for API endpoints
- Input validation on all user inputs

## Deployment

### Local Development
```bash
npm run dev
```

### Production Build
```bash
npm run build
npm start
```

### Environment Variables (Optional)
You can also use environment variables instead of the account.json file:
```bash
export GOOGLE_APPLICATION_CREDENTIALS="/path/to/account.json"
```

## Troubleshooting

### Common Issues

1. **Authentication Errors**
   - Verify service account JSON file location
   - Check service account permissions
   - Ensure project ID is correct

2. **API Connection Errors**
   - Verify reasoning engine ID
   - Check network connectivity
   - Confirm Vertex AI Agent Engine is enabled

3. **Session Management Issues**
   - Clear browser localStorage
   - Check user_id format
   - Verify session permissions

### Debug Mode

Enable debug logging by setting:
```bash
export DEBUG=true
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is licensed under the MIT License.

## Support

For issues and questions:
1. Check the troubleshooting section
2. Review Google Cloud Vertex AI documentation
3. Check the ADK documentation
4. Create an issue in the repository

---

**Note**: This application requires a valid Google Cloud setup with Vertex AI Agent Engine and a deployed ADK agent to function properly.

