import axios, { AxiosResponse } from 'axios';
import { GoogleCloudAuth } from './auth';

export interface Session {
  name: string;
  createTime: string;
  updateTime: string;
  ttl?: string;
}

export interface StreamQueryRequest {
  user_id: string;
  session_id?: string;
  message: string;
}

export interface CreateSessionRequest {
  user_id: string;
  ttl?: string;
}

export interface SessionOperationRequest {
  user_id: string;
  session_id: string;
}

export interface ListSessionsRequest {
  user_id: string;
  page_size?: number;
  page_token?: string;
}

export class VertexAIClient {
  private auth: GoogleCloudAuth;
  private baseUrl: string;
  private streamUrl: string;
  private projectId: string = 'adk-gp';
  private location: string = 'us-central1';
  private reasoningEngineId: string = '1740192655234564096';

  constructor() {
    this.auth = new GoogleCloudAuth();
    this.baseUrl = `https://${this.location}-aiplatform.googleapis.com/v1/projects/${this.projectId}/locations/${this.location}/reasoningEngines/${this.reasoningEngineId}:query`;
    this.streamUrl = `https://${this.location}-aiplatform.googleapis.com/v1/projects/${this.projectId}/locations/${this.location}/reasoningEngines/${this.reasoningEngineId}:streamQuery`;
  }

  private async getAuthHeaders(): Promise<{ [key: string]: string }> {
    const accessToken = await this.auth.getAccessToken();
    return {
      'Authorization': `Bearer ${accessToken}`,
      'Content-Type': 'application/json',
    };
  }

  async createSession(request: CreateSessionRequest): Promise<Session> {
    try {
      const headers = await this.getAuthHeaders();
      const response: AxiosResponse<Session> = await axios.post(
        this.baseUrl,
        {
          class_method: 'create_session',
          input: {
            user_id: request.user_id,
            ttl: request.ttl,
          },
        },
        { headers }
      );
      return response.data;
    } catch (error) {
      console.error('Error creating session:', error);
      throw error;
    }
  }

  async listSessions(request: ListSessionsRequest): Promise<{ sessions: Session[]; nextPageToken?: string }> {
    try {
      const headers = await this.getAuthHeaders();
      const response = await axios.post(
        this.baseUrl,
        {
          class_method: 'list_sessions',
          input: {
            user_id: request.user_id,
            page_size: request.page_size,
            page_token: request.page_token,
          },
        },
        { headers }
      );
      console.log('VertexAI listSessions response:', JSON.stringify(response.data, null, 2));
      return response.data;
    } catch (error: any) {
      console.error('Error listing sessions:', JSON.stringify(error, null, 2));
      throw error;
    }
  }

  async getSession(request: SessionOperationRequest): Promise<Session> {
    try {
      const headers = await this.getAuthHeaders();
      const response: AxiosResponse<Session> = await axios.post(
        this.baseUrl,
        {
          class_method: 'get_session',
          input: {
            user_id: request.user_id,
            session_id: request.session_id,
          },
        },
        { headers }
      );
      return response.data;
    } catch (error) {
      console.error('Error getting session:', error);
      throw error;
    }
  }

  async deleteSession(request: SessionOperationRequest): Promise<void> {
    try {
      const headers = await this.getAuthHeaders();
      await axios.post(
        this.baseUrl,
        {
          class_method: 'delete_session',
          input: {
            user_id: request.user_id,
            session_id: request.session_id,
          },
        },
        { headers }
      );
    } catch (error) {
      console.error('Error deleting session:', error);
      throw error;
    }
  }

  async *streamQuery(request: StreamQueryRequest): AsyncGenerator<unknown, void, unknown> {
    try {
      const headers = await this.getAuthHeaders();
      const response = await axios.post(
        this.streamUrl,
        {
          class_method: 'stream_query',
          input: {
            user_id: request.user_id,
            session_id: request.session_id,
            message: request.message,
          },
        },
        {
          headers,
          responseType: 'stream',
        }
      );

      const stream = response.data;
      let buffer = '';

      for await (const chunk of stream) {
        buffer += chunk.toString();
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.trim()) {
            try {
              const data = JSON.parse(line);
              yield data;
            } catch {
              // Skip invalid JSON lines
              console.warn('Failed to parse streaming response line:', line);
            }
          }
        }
      }

      // Process any remaining buffer
      if (buffer.trim()) {
        try {
          const data = JSON.parse(buffer);
          yield data;
        } catch {
          console.warn('Failed to parse final buffer:', buffer);
        }
      }
    } catch (error) {
      console.error('Error streaming query:', error);
      throw error;
    }
  }
}

