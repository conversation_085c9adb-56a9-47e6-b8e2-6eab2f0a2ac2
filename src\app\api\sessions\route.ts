import { NextRequest, NextResponse } from 'next/server';
import { VertexAIClient } from '@/lib/vertexai';

const vertexAI = new VertexAIClient();

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action, user_id, session_id, ttl, page_size, page_token } = body;

    if (!user_id) {
      return NextResponse.json(
        { error: 'user_id is required' },
        { status: 400 }
      );
    }

    switch (action) {
      case 'create':
        const newSession = await vertexAI.createSession({ user_id, ttl });
        return NextResponse.json(newSession);

      case 'list':
        const sessions = await vertexAI.listSessions({ 
          user_id, 
          page_size, 
          page_token 
        });
        return NextResponse.json(sessions);

      case 'get':
        if (!session_id) {
          return NextResponse.json(
            { error: 'session_id is required for get action' },
            { status: 400 }
          );
        }
        const session = await vertexAI.getSession({ user_id, session_id });
        return NextResponse.json(session);

      case 'delete':
        if (!session_id) {
          return NextResponse.json(
            { error: 'session_id is required for delete action' },
            { status: 400 }
          );
        }
        await vertexAI.deleteSession({ user_id, session_id });
        return NextResponse.json({ success: true });

      default:
        return NextResponse.json(
          { error: 'Invalid action. Must be one of: create, list, get, delete' },
          { status: 400 }
        );
    }
  } catch (error) {
    console.error('Sessions API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    console.log("Request URL", request.url);
    const { searchParams } = new URL(request.url);
    const user_id = searchParams.get('user_id');
    const page_size = searchParams.get('page_size');
    const page_token = searchParams.get('page_token');

    console.log("User ID", user_id);
    if (!user_id) {
      return NextResponse.json(
        { error: 'user_id is required' },
        { status: 400 }
      );
    }

    const sessions = await vertexAI.listSessions({ 
      user_id, 
      page_size: page_size ? parseInt(page_size) : undefined,
      page_token: page_token || undefined
    });
    
    return NextResponse.json(sessions ? sessions.sessions : []);
  } catch (error) {
    console.error('Sessions GET API error:', JSON.stringify(error, null, 2));
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

